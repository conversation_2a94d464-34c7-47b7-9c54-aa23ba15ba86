<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学公式渲染测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-case {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .input {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
        }
        .output {
            background: #fff;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #eee;
            border-radius: 3px;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>数学公式渲染测试</h1>
    
    <div class="test-case">
        <h3>测试案例 1: 带单引号的公式</h3>
        <div class="input">输入: $\sigma' = P_n = (\epsilon_r - 1)\epsilon_0 E$</div>
        <div class="output" id="test1"></div>
    </div>
    
    <div class="test-case">
        <h3>测试案例 2: 带大于小于号的公式</h3>
        <div class="input">输入: 顺磁质 $\mu_r > 1$，抗磁质 $\mu_r < 1$</div>
        <div class="output" id="test2"></div>
    </div>
    
    <div class="test-case">
        <h3>测试案例 3: 块级公式</h3>
        <div class="input">输入: $$\nabla \times \mathbf{E} = -\frac{\partial \mathbf{B}}{\partial t}$$</div>
        <div class="output" id="test3"></div>
    </div>
    
    <div class="test-case">
        <h3>测试案例 4: 混合内容</h3>
        <div class="input">输入: 电场强度 $E > 0$ 时，电势能 $U = qV$ 其中 $q' = \epsilon_0 E$</div>
        <div class="output" id="test4"></div>
    </div>

    <div class="test-case">
        <h3>测试案例 5: 复杂符号组合</h3>
        <div class="input">输入: 当 $\alpha' > \beta'$ 且 $\gamma < \delta$ 时，有 $f'(x) = \frac{d}{dx}[g(x)]$</div>
        <div class="output" id="test5"></div>
    </div>

    <div class="test-case">
        <h3>测试案例 6: 带引号和比较符号的块级公式</h3>
        <div class="input">输入: $$\sigma' = P_n = (\epsilon_r - 1)\epsilon_0 E \quad \text{当} \quad \mu_r > 1$$</div>
        <div class="output" id="test6"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.js"></script>
    
    <script>
        // 修复后的渲染函数
        function renderMathContent(content) {
            // 先提取和保护数学公式，避免被 marked.js 转义
            const mathPlaceholders = [];
            let mathIndex = 0;

            // 提取块级数学公式 $$...$$
            content = content.replace(/\$\$([\s\S]*?)\$\$/g, function(match, formula) {
                const placeholder = `__MATH_BLOCK_${mathIndex}__`;
                mathPlaceholders[mathIndex] = {
                    type: 'block',
                    formula: formula.trim(),
                    placeholder: placeholder
                };
                mathIndex++;
                return placeholder;
            });

            // 提取行内数学公式 $...$
            content = content.replace(/\$([^$\n]+?)\$/g, function(match, formula) {
                const placeholder = `__MATH_INLINE_${mathIndex}__`;
                mathPlaceholders[mathIndex] = {
                    type: 'inline',
                    formula: formula.trim(),
                    placeholder: placeholder
                };
                mathIndex++;
                return placeholder;
            });

            // 使用marked.js进行Markdown编译
            let html = marked.parse(content);

            // 恢复数学公式并用KaTeX渲染
            mathPlaceholders.forEach(function(mathItem) {
                if (mathItem) {
                    try {
                        const isDisplayMode = mathItem.type === 'block';
                        const renderedMath = katex.renderToString(mathItem.formula, {displayMode: isDisplayMode});
                        html = html.replace(mathItem.placeholder, renderedMath);
                    } catch (e) {
                        const errorMsg = `<span class="error">数学公式错误: ${e.message}</span>`;
                        html = html.replace(mathItem.placeholder, errorMsg);
                    }
                }
            });

            return html;
        }

        // 测试用例
        const testCases = [
            "$\\sigma' = P_n = (\\epsilon_r - 1)\\epsilon_0 E$",
            "顺磁质 $\\mu_r > 1$，抗磁质 $\\mu_r < 1$",
            "$$\\nabla \\times \\mathbf{E} = -\\frac{\\partial \\mathbf{B}}{\\partial t}$$",
            "电场强度 $E > 0$ 时，电势能 $U = qV$ 其中 $q' = \\epsilon_0 E$",
            "当 $\\alpha' > \\beta'$ 且 $\\gamma < \\delta$ 时，有 $f'(x) = \\frac{d}{dx}[g(x)]$",
            "$$\\sigma' = P_n = (\\epsilon_r - 1)\\epsilon_0 E \\quad \\text{当} \\quad \\mu_r > 1$$"
        ];

        // 渲染测试结果
        testCases.forEach((testCase, index) => {
            const outputElement = document.getElementById(`test${index + 1}`);
            if (outputElement) {
                try {
                    const result = renderMathContent(testCase);
                    outputElement.innerHTML = result;
                } catch (error) {
                    outputElement.innerHTML = `<span class="error">渲染错误: ${error.message}</span>`;
                }
            }
        });
    </script>
</body>
</html>
